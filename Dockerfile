# Multi-stage build for buildfs
FROM rust:1.80-slim-bookworm AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libclang-dev \
    clang \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /usr/src/buildfs

# Copy Cargo files first for better caching
COPY Cargo.toml Cargo.lock ./

# Create a dummy main.rs to build dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release && rm -rf src

# Copy the actual source code
COPY src ./src

# Build the actual application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    # Filesystem tools
    e2fsprogs \
    btrfs-progs \
    squashfs-tools \
    dosfstools \
    xfsprogs \
    # System utilities
    util-linux \
    mount \
    sudo \
    # Container engines
    docker.io \
    # Network tools (for downloading)
    curl \
    wget \
    ca-certificates \
    # Process management
    procps \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user with sudo privileges
RUN useradd -m -s /bin/bash buildfs && \
    echo "buildfs ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Copy the built binary
COPY --from=builder /usr/src/buildfs/target/release/buildfs /usr/local/bin/buildfs

# Create necessary directories
RUN mkdir -p /workspace /output && \
    chown buildfs:buildfs /workspace /output

# Switch to non-root user
USER buildfs
WORKDIR /workspace

# Set environment variables
ENV PATH="/usr/local/bin:$PATH"

# Default command
CMD ["buildfs", "--help"]
