#!/bin/bash

# buildfs-docker.sh - Wrapper script to run buildfs in Docker
# Usage: ./buildfs-docker.sh [buildfs arguments]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p output

# Build the Docker image if it doesn't exist or if --build flag is passed
if [[ "$1" == "--build" ]] || ! docker image inspect buildfs-buildfs >/dev/null 2>&1; then
    if [[ "$1" == "--build" ]]; then
        shift  # Remove --build from arguments
    fi
    print_info "Building buildfs Docker image..."
    docker-compose build buildfs
fi

# Check if Docker socket access is requested
USE_DOCKER_SOCKET=false
if [[ "$1" == "--with-docker" ]]; then
    USE_DOCKER_SOCKET=true
    shift  # Remove --with-docker from arguments
    print_warning "Using Docker socket access. This requires Docker Desktop configuration."
    print_info "If you get permission errors, see README-Docker.md for setup instructions."
fi

# Choose the appropriate service
SERVICE="buildfs"
if [[ "$USE_DOCKER_SOCKET" == "true" ]]; then
    SERVICE="buildfs-with-docker"
    export COMPOSE_PROFILES=docker
fi

# Check if we're running an interactive command or help
if [[ $# -eq 0 ]] || [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]]; then
    print_info "Running buildfs in interactive mode..."
    docker-compose run --rm "$SERVICE" buildfs "$@"
else
    # For actual build commands, we need privileged mode
    print_info "Running buildfs command: buildfs $*"
    print_warning "This requires privileged Docker access for mounting operations."

    # Run the command
    docker-compose run --rm "$SERVICE" sudo buildfs "$@"
fi

print_info "buildfs execution completed."
