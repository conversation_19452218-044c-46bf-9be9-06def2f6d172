# Minimal Debian root filesystem example
# This creates a bootable Debian root filesystem from a Docker image

[filesystem]
type = "Ext4"
size_mib = 250

[container]
engine = "Docker"
rootful = true
wait_timeout_s = 30
image = { name = "docker.io/library/debian", tag = "bookworm-slim" }

[[commands]]
script_inline = """
#!/bin/bash
set -e

# Update package lists
apt update

# Install essential packages
apt install -y \
    udev \
    systemd-sysv \
    iputils-ping \
    curl \
    nano \
    bash-completion

# Remove unnecessary systemd services
rm -f /etc/systemd/system/multi-user.target.wants/systemd-resolved.service
rm -f /etc/systemd/system/dbus-org.freedesktop.resolve1.service
rm -f /etc/systemd/system/sysinit.target.wants/systemd-timesyncd.service

# Disable some services
systemctl disable e2scrub_reap.service || true
rm -vf /etc/systemd/system/timers.target.wants/* || true

# Configure serial console for VM access
for console in ttyS0; do
    mkdir -p "/etc/systemd/system/serial-getty@$console.service.d/"
    cat <<'EOF' > "/etc/systemd/system/serial-getty@$console.service.d/override.conf"
[Service]
# systemd requires this empty ExecStart line to override
ExecStart=
ExecStart=-/sbin/agetty --autologin root -o '-p -- \\u' --keep-baud 115200,38400,9600 %I dumb
EOF
done

# Remove root password for easy access
passwd -d root

# Clean up to reduce image size
rm -rf /usr/share/{doc,man,info,locale}
apt clean
rm -rf /var/lib/apt/lists/*

# Add security configuration
cat >> /etc/sysctl.conf <<EOF
# Security: disable unprivileged BPF
kernel.unprivileged_bpf_disabled=1
EOF

echo "Debian minimal setup completed successfully!"
"""

# Add DNS configuration
[[overlays]]
source_inline = """
nameserver 8.8.8.8
nameserver 8.8.4.4
nameserver 1.1.1.1
"""
destination = "/etc/resolv.conf"

# Add a welcome message
[[overlays]]
source_inline = """
Welcome to Debian Minimal Root Filesystem!
Built with buildfs - https://github.com/kanpov/buildfs

This is a minimal Debian system suitable for VMs.
Default user: root (no password)
"""
destination = "/etc/motd"

# Export configuration - what to include in the final filesystem
[export.directories]
include = [ 
    "/bin", 
    "/etc", 
    "/home", 
    "/lib", 
    "/lib64", 
    "/root", 
    "/sbin", 
    "/usr" 
]
create = [ 
    "/var/lib/dpkg", 
    "/dev", 
    "/proc", 
    "/sys", 
    "/run", 
    "/tmp", 
    "/var/lib/systemd",
    "/var/log",
    "/var/tmp"
]
