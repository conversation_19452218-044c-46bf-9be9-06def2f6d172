# Minimal Alpine Linux root filesystem example
# Alpine is much smaller than Debian, good for containers and VMs

[filesystem]
type = "Ext4"
size_mib = 150

[container]
engine = "Docker"
rootful = true
wait_timeout_s = 20
image = { name = "docker.io/library/alpine", tag = "latest" }

[[commands]]
script_inline = """
#!/bin/sh
set -e

# Update package index
apk update

# Install essential packages
apk add --no-cache \
    openrc \
    util-linux \
    busybox-initscripts \
    busybox-openrc \
    alpine-baselayout \
    alpine-keys \
    ca-certificates \
    curl \
    nano

# Configure OpenRC
rc-update add devfs boot
rc-update add dmesg boot
rc-update add mdev boot
rc-update add hwclock boot
rc-update add modules boot
rc-update add sysctl boot
rc-update add hostname boot
rc-update add bootmisc boot
rc-update add syslog boot

rc-update add mount-ro shutdown
rc-update add killprocs shutdown
rc-update add savecache shutdown

# Configure root login
echo "root::" | chpasswd -e

# Set hostname
echo "alpine-buildfs" > /etc/hostname

# Configure serial console
echo "ttyS0::respawn:/sbin/getty -L ttyS0 115200 vt100" >> /etc/inittab

# Clean up
rm -rf /var/cache/apk/*

echo "Alpine minimal setup completed successfully!"
"""

# DNS configuration
[[overlays]]
source_inline = """
nameserver *******
nameserver *******
"""
destination = "/etc/resolv.conf"

# Welcome message
[[overlays]]
source_inline = """
Welcome to Alpine Linux Minimal Root Filesystem!
Built with buildfs

This is a minimal Alpine system suitable for containers and VMs.
Default user: root (no password)
"""
destination = "/etc/motd"

# Export configuration
[export.directories]
include = [ 
    "/bin", 
    "/etc", 
    "/home", 
    "/lib", 
    "/root", 
    "/sbin", 
    "/usr",
    "/var"
]
create = [ 
    "/dev", 
    "/proc", 
    "/sys", 
    "/run", 
    "/tmp",
    "/mnt"
]
