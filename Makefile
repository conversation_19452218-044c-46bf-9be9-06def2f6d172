# Makefile for buildfs Docker operations

.PHONY: help build run clean examples test

# Default target
help:
	@echo "buildfs Docker Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  build     - Build the Docker image"
	@echo "  run       - Run buildfs interactively"
	@echo "  examples  - Build all example configurations"
	@echo "  clean     - Clean up Docker images and output"
	@echo "  test      - Test the Docker setup"
	@echo "  help      - Show this help message"

# Build the Docker image
build:
	@echo "Building buildfs Docker image..."
	docker-compose build buildfs

# Run buildfs interactively
run:
	@echo "Starting buildfs container..."
	docker-compose run --rm buildfs bash

# Build all examples
examples: build
	@echo "Building all example configurations..."
	@mkdir -p output
	./buildfs-docker.sh run -o /output/debian-minimal.ext4 /workspace/examples/debian-minimal.toml
	./buildfs-docker.sh run -o /output/alpine-minimal.ext4 /workspace/examples/alpine-minimal.toml
	@echo "Examples built successfully!"
	@ls -la output/

# Test the Docker setup
test: build
	@echo "Testing buildfs Docker setup..."
	./buildfs-docker.sh --help
	./buildfs-docker.sh dry-run /workspace/examples/debian-minimal.toml
	@echo "Test completed successfully!"

# Clean up
clean:
	@echo "Cleaning up Docker images and output..."
	docker-compose down --rmi all --volumes --remove-orphans || true
	docker system prune -f
	rm -rf output/*
	@echo "Cleanup completed!"

# Quick build of Debian example
debian: build
	@mkdir -p output
	./buildfs-docker.sh run -o /output/debian-minimal.ext4 /workspace/examples/debian-minimal.toml

# Quick build of Alpine example  
alpine: build
	@mkdir -p output
	./buildfs-docker.sh run -o /output/alpine-minimal.ext4 /workspace/examples/alpine-minimal.toml
