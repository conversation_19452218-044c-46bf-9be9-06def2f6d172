services:
  # Basic buildfs service without Docker socket (safer, works out of the box)
  buildfs:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: buildfs-container
    privileged: true  # Required for mounting operations
    volumes:
      # Mount current directory for build scripts
      - ./examples:/workspace/examples:ro
      - ./output:/output
      # Mount host's /tmp for temporary files
      - /tmp:/tmp
    environment:
      - RUST_LOG=info
    working_dir: /workspace
    # Keep container running for interactive use
    tty: true
    stdin_open: true

  # Service with Docker socket access (requires Docker Desktop configuration)
  buildfs-with-docker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: buildfs-with-docker-container
    privileged: true  # Required for mounting operations
    volumes:
      # Mount current directory for build scripts
      - ./examples:/workspace/examples:ro
      - ./output:/output
      # Mount Docker socket for container operations (requires allowlist)
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount host's /tmp for temporary files
      - /tmp:/tmp
    environment:
      - RUST_LOG=info
    working_dir: /workspace
    # Keep container running for interactive use
    tty: true
    stdin_open: true
    profiles:
      - docker

  # Alternative service for one-off builds
  buildfs-runner:
    build:
      context: .
      dockerfile: Dockerfile
    privileged: true
    volumes:
      - ./examples:/workspace/examples:ro
      - ./output:/output
      - /tmp:/tmp
    environment:
      - RUST_LOG=info
    working_dir: /workspace
    profiles:
      - runner
