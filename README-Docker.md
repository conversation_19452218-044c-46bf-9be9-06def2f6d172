# buildfs Docker Setup

This directory contains Docker configuration to run `buildfs` in a Linux environment, which is necessary since `buildfs` requires Linux-specific system calls and tools.

## Prerequisites

- Docker Desktop for Mac (or Docker Engine on Linux)
- Docker Compose (usually included with Docker Desktop)

## Quick Start

1. **Build the Docker image:**
   ```bash
   ./buildfs-docker.sh --build
   ```

2. **Run buildfs with help:**
   ```bash
   ./buildfs-docker.sh --help
   ```

3. **Build a sample root filesystem:**
   ```bash
   ./buildfs-docker.sh run -o /output/debian.ext4 /workspace/examples/debian-minimal.toml
   ```

4. **Check the output:**
   ```bash
   ls -la output/
   ```

## Files Overview

### Docker Configuration
- `Dockerfile` - Multi-stage build for buildfs with all required Linux tools
- `docker-compose.yml` - Docker Compose configuration for easy management
- `buildfs-docker.sh` - Wrapper script to simplify running buildfs commands

### Examples
- `examples/debian-minimal.toml` - Minimal Debian root filesystem
- `examples/alpine-minimal.toml` - Minimal Alpine Linux root filesystem

### Output
- `output/` - Directory where generated filesystem images are stored

## Usage Examples

### Basic Commands

```bash
# Show help
./buildfs-docker.sh --help

# Dry run (validate configuration without building)
./buildfs-docker.sh dry-run /workspace/examples/debian-minimal.toml

# Build Debian minimal filesystem
./buildfs-docker.sh run -o /output/debian-minimal.ext4 /workspace/examples/debian-minimal.toml

# Build Alpine minimal filesystem  
./buildfs-docker.sh run -o /output/alpine-minimal.ext4 /workspace/examples/alpine-minimal.toml
```

### Interactive Mode

For debugging or development, you can run the container interactively:

```bash
# Start interactive container
docker-compose run --rm buildfs bash

# Inside the container, you can run buildfs directly:
sudo buildfs run -o /output/test.ext4 /workspace/examples/debian-minimal.toml
```

### Custom Build Scripts

1. Create your build script in the `examples/` directory
2. Run it with the wrapper script:
   ```bash
   ./buildfs-docker.sh run -o /output/my-custom.ext4 /workspace/examples/my-script.toml
   ```

## Docker Image Details

The Docker image includes:

### Build Tools
- Rust 1.80+ toolchain
- Clang and build essentials
- pkg-config

### Runtime Tools
- **Filesystem tools**: e2fsprogs, btrfs-progs, squashfs-tools, dosfstools, xfsprogs
- **System utilities**: util-linux, mount, sudo
- **Container engine**: Docker (for nested container operations)
- **Network tools**: curl, wget, ca-certificates

### Security
- Runs as non-root user `buildfs` with sudo privileges
- Privileged mode required for mounting operations
- Docker socket mounted for container operations

## Troubleshooting

### Permission Issues
If you encounter permission issues:
```bash
# Rebuild the image
./buildfs-docker.sh --build

# Check Docker permissions
docker run --rm -it buildfs-buildfs whoami
```

### Docker Socket Issues
If Docker operations fail inside the container:
```bash
# Ensure Docker is running on host
docker info

# Check socket permissions
ls -la /var/run/docker.sock
```

### Build Failures
For build failures:
```bash
# Run with verbose logging
RUST_LOG=debug ./buildfs-docker.sh run -o /output/debug.ext4 /workspace/examples/debian-minimal.toml

# Check container logs
docker-compose logs buildfs
```

## Advanced Usage

### Custom Dockerfile
You can modify the `Dockerfile` to add additional tools or change the base image.

### Volume Mounts
The Docker setup mounts:
- `./examples` → `/workspace/examples` (read-only)
- `./output` → `/output` (read-write)
- `/var/run/docker.sock` → `/var/run/docker.sock` (Docker socket)
- `/tmp` → `/tmp` (temporary files)

### Environment Variables
- `RUST_LOG` - Set logging level (trace, debug, info, warn, error)

## Testing the Generated Images

Once you have generated a filesystem image, you can test it with QEMU:

```bash
# Install QEMU (on macOS)
brew install qemu

# Boot the generated image
qemu-system-x86_64 \
  -drive format=raw,file=output/debian-minimal.ext4 \
  -m 512M \
  -nographic \
  -append "console=ttyS0 root=/dev/sda rw"
```

## Contributing

To add new example configurations:
1. Create a new `.toml` file in the `examples/` directory
2. Test it with the Docker setup
3. Document any special requirements

## Notes

- The container requires privileged mode for mounting operations
- Generated images are owned by the container user but should be accessible on the host
- Large images may take significant time to build depending on the complexity of the build script
