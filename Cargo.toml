[package]
name = "buildfs"
version = "0.3.4"
edition = "2021"
description = "A CI-compatible CLI utility that can create root filesystem images (for use in virtual machines) from reproducible and readable TOML build scripts"
license = "MIT"
keywords = ["firecracker", "cli", "rootfs", "docker", "podman"]
categories = ["virtualization", "filesystem", "command-line-utilities"]
repository = "https://github.com/kanpov/buildfs"
readme = "README.md"
rust-version = "1.80.0"

[profile.dev]
panic = "abort"
debug = false
strip = "symbols"

[profile.release]
lto = "fat"
strip = "symbols"
codegen-units = 1

[dependencies]
async-trait = "0.1.83"
bollard = "0.18.1"
clap = { version = "4.5.32", features = ["derive"] }
colored = "3.0.0"
flate2 = "1.1.0"
fs_extra = "1.3.0"
futures-util = "0.3.31"
hyper = "1.6.0"
hyper-util = { version = "0.1.10", features = ["tokio"] }
libc = "0.2.171"
log = "0.4.26"
podman-rest-client = { version = "0.13.0", default-features = false, features = [
    "v5",
    "uds",
] }
serde = { version = "1.0.219", features = ["derive"] }
simple_logger = "5.0.0"
sys-mount = "3.0.1"
tar = "0.4.44"
tokio = { version = "1.44.1", features = [
    "rt-multi-thread",
    "process",
    "macros",
    "fs",
] }
toml = "0.8.20"
uuid = { version = "1.16.0", features = ["v4"] }
which = "7.0.2"
